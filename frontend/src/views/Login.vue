<template>
  <div class="min-h-screen w-full flex items-center justify-center p-4">
    <div class="max-w-md w-full mx-auto p-8 bg-gray-900/70 backdrop-blur-md rounded-xl border border-cyan-500/30 shadow-glow-md">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2">
          HERBIT
        </h1>
        <p class="text-gray-400">Sign in to access your account</p>
      </div>

      <Button
        @click="login"
        variant="login"
      >
        <div class="flex items-center justify-center">
          <svg class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
          </svg>
          <span>Login with Dex</span>
        </div>
      </Button>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { debug, info, logUserAction } from '@/utils/logger';
import { Button } from '@/components/ui/button';
import { api } from '@/services/api';

const router = useRouter();

// Check if user is already authenticated on mount
onMounted(async () => {
  try {
    // Use cached userinfo to check authentication status
    const data = await api.getCachedUserInfo(false);
    if (data.authenticated) {
      // If already authenticated, redirect to home
      router.push('/');
    }
  } catch (error) {
    // If there's an error checking auth status, stay on login page
    debug('Error checking auth status:', error);
  }
});

const login = () => {
  // Use environment variables if available, otherwise fallback to defaults
  const clientId = import.meta.env.VITE_AUTH_CLIENT_ID;
  const redirectUri = import.meta.env.VITE_AUTH_CALLBACK_URL;
  const authUrl = import.meta.env.VITE_AUTH_LOGIN_URL;

  // Generate a random state value and store it in localStorage
  const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  localStorage.setItem('oauth_state', state);

  // Construct the full authorization URL with all required parameters
  const fullAuthUrl = `${authUrl}?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=openid+email+profile+groups&state=${state}`;

  debug('Redirecting to auth URL', { fullAuthUrl, state });
  logUserAction('login_attempt', { authUrl: fullAuthUrl });

  window.location.href = fullAuthUrl;
};
</script>
